# 🤖 Smart Grow A Garden Bot

Bot Telegram cerdas untuk monitoring game **Grow A Garden** di Roblox dengan sistem deteksi perubahan real-time.

## ✨ Fitur Utama

- 🧠 **Intelligent Detection System** - <PERSON>ya mengirim notifikasi saat ada perubahan
- 📊 **Real-time Monitoring** - Memantau stock gear, seeds, eggs, dan cuaca
- ⚡ **Efisien** - Cek setiap 2 menit, kirim hanya saat berubah
- 🌦️ **Weather Tracking** - Monitor cuaca dan efeknya
- 📈 **Update Berkala** - Update otomatis setiap 30 menit meski tidak ada perubahan

## 🚀 Instalasi

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd gag
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Konfigurasi Bot**
   - Edit `bot.js` dan sesuaikan:
     - `TELEGRAM_TOKEN` - Token bot Telegram Anda
     - `CHAT_ID` - ID chat/group tujuan notifikasi

4. **Jalankan Bot**
   ```bash
   npm start
   # atau
   node bot.js
   ```

## 🔧 Konfigurasi

### Environment Variables (Opsional)
```bash
export TELEGRAM_TOKEN="your_bot_token_here"
export CHAT_ID="your_chat_id_here"
```

### API Endpoint
Bot menggunakan API resmi Grow A Garden:
- **Base URL**: `https://gagapi.onrender.com`
- **Endpoints**:
  - `/gear` - Data gear shop
  - `/seeds` - Data seed shop  
  - `/eggs` - Data egg shop
  - `/weather` - Data cuaca dan efek

## 📱 Perintah Bot

| Perintah | Deskripsi |
|----------|-----------|
| `/start` | Menampilkan pesan selamat datang dan info bot |
| `/cek` | Force update manual (ambil data terbaru) |
| `/status` | Cek status koneksi bot dan API |
| `/stats` | Lihat statistik penggunaan bot |
| `/help` | Bantuan dan panduan penggunaan |

## 🧠 Cara Kerja Smart Detection

1. **Monitoring Berkala**: Bot mengecek API setiap 2 menit
2. **Hash Comparison**: Membandingkan hash data baru dengan data sebelumnya
3. **Change Detection**: Hanya mengirim notifikasi jika ada perubahan pada:
   - Stock items (gear/seed/egg)
   - Cuaca dan efeknya
4. **Periodic Updates**: Update berkala setiap 30 menit meski tidak ada perubahan

## 📊 Format Pesan

```
🔥 PERUBAHAN BESAR! Stock & Cuaca Berubah

🌦️ Cuaca Sekarang: rain ✅ 🌟
🧬 Efek Cuaca: Rain increases growth speed
• 50% chance to make fruit wet (x2 value) 🌟

🧰 Gear Shop: 🆕
• Advanced Sprinkler (2x)
• Cleaning Spray (3x)
• Recall Wrench (2x)

🌱 Seed Shop: 🆕
• Tomato Seed (5x)
• Carrot Seed (3x)

🥚 Egg Shop: 🆕
• Chicken Egg (2x)
• Duck Egg (1x)

📅 Update: 27/07/2025 16:04:02

🤖 Smart Bot - Update #1 | Hanya kirim saat ada perubahan
```

## 🛠️ Troubleshooting

### Bot Tidak Berjalan
- Pastikan Node.js terinstall
- Cek token Telegram dan chat ID
- Pastikan koneksi internet stabil

### Tidak Menerima Notifikasi
- Cek chat ID sudah benar
- Pastikan bot sudah di-add ke group (jika menggunakan group)
- Cek log error di console

### API Error
- API menggunakan `gagapi.onrender.com` (bukan `gagapi.com`)
- Jika API down, bot akan tetap berjalan dan mencoba reconnect

## 📝 Log Output

```
🚀 Memulai Smart Grow A Garden Bot...
🧠 Mode: Intelligent Detection System
✅ API connection successful
✅ Telegram connection successful
🤖 Smart Bot siap dan berjalan...
🔍 Cek perubahan setiap 2 menit
🚨 Restock alert setiap 1 menit
📊 Sending initial update...
🔍 Mengecek perubahan data...
📊 Perubahan terdeteksi: stock, weather, hasAnyChange
✅ Update #1 berhasil dikirim
```

## 🔄 Update & Maintenance

Bot akan otomatis:
- Reconnect jika koneksi terputus
- Handle API errors dengan graceful fallback
- Kirim error notification jika ada masalah
- Maintain uptime statistics

## 👨‍💻 Developer

**ONAD** - Smart Detection System

## 📄 License

MIT License - Bebas digunakan dan dimodifikasi
