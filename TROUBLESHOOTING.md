# 🛠️ Troubleshooting Guide - <PERSON> Grow A Garden Bot

## ❌ Error 409 Conflict: Multiple Bot Instances

### 🔍 **Masalah:**
```
❌ Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
```

### ✅ **Solusi:**

#### 1. **Gunakan Script Restart (Recommended)**
```bash
# Double-click atau jalankan:
restart_bot.bat
```

#### 2. **Manual Cleanup**
```bash
# Hentikan semua Node.js processes
taskkill /f /im node.exe

# Hapus lock file
del bot.lock

# Tunggu 5 detik
timeout /t 5

# Start bot
node bot.js
```

#### 3. **Cek Status Bot**
```bash
# Gunakan script checker
check_bot_status.bat
```

## 🔒 Lock File System

Bot menggunakan sistem lock file (`bot.lock`) untuk mencegah multiple instance:

- ✅ **Lock file dibuat** saat bot start dengan PID process
- ✅ **Cek otomatis** apakah bot sudah berjalan
- ✅ **Auto cleanup** saat bot dihentikan
- ✅ **Stale lock detection** untuk lock file yang tertinggal

### 📋 **Status Lock File:**

```bash
🔒 Lock file created with PID: 3860    # ✅ Normal
❌ Bot sudah berjalan dengan PID: 3860  # ⚠️ Multiple instance dicegah
🔓 Lock file removed                    # ✅ Cleanup berhasil
```

## 🌐 API Connection Issues

### 🔍 **Masalah:**
```
❌ Startup error: getaddrinfo ENOTFOUND gagapi.com
```

### ✅ **Solusi:**
- ✅ Bot sudah diperbaiki menggunakan `gagapi.onrender.com`
- ✅ Auto-retry jika API tidak tersedia
- ✅ Error notification dikirim ke Telegram

## 📱 Telegram Bot Issues

### 🔍 **Masalah:**
- Bot tidak merespon command
- Pesan tidak terkirim

### ✅ **Solusi:**

#### 1. **Cek Token & Chat ID**
```javascript
// Di bot.js, pastikan benar:
const TELEGRAM_TOKEN = 'your_bot_token_here';
const CHAT_ID = 'your_chat_id_here';
```

#### 2. **Test Connection**
```bash
# Jalankan bot dan lihat log:
✅ Telegram connection successful  # ✅ OK
❌ Telegram connection failed     # ❌ Cek token
```

#### 3. **Bot Commands**
```
/start  - Test bot responsif
/status - Cek koneksi API
/cek    - Force update manual
```

## 🔄 Bot Restart Scenarios

### 1. **Normal Restart**
```bash
# Ctrl+C untuk stop, lalu:
node bot.js
```

### 2. **Force Restart (Conflict)**
```bash
restart_bot.bat
```

### 3. **Emergency Stop**
```bash
taskkill /f /im node.exe
del bot.lock
```

## 📊 Monitoring Bot Health

### ✅ **Tanda Bot Sehat:**
```
🚀 Memulai Smart Grow A Garden Bot...
🧠 Mode: Intelligent Detection System
✅ API connection successful
✅ Telegram connection successful
🤖 Smart Bot siap dan berjalan...
✅ Update #1 berhasil dikirim
```

### ❌ **Tanda Bot Bermasalah:**
```
❌ Polling error: 409 Conflict        # Multiple instance
❌ API connection failed               # API down
❌ Telegram connection failed          # Token/network issue
❌ Error dalam intelligent update      # Data parsing error
```

## 🚨 Emergency Commands

### **Stop All Bots**
```bash
# Windows
taskkill /f /im node.exe

# Linux/Mac
pkill -f "node bot.js"
```

### **Clean Reset**
```bash
# Hapus semua file temporary
del bot.lock
del *.log

# Restart fresh
node bot.js
```

## 📞 Support

Jika masalah masih berlanjut:

1. **Cek log error** di console
2. **Screenshot error message**
3. **Cek file `bot.lock`** apakah ada
4. **Test API manual**: `curl https://gagapi.onrender.com/gear`
5. **Restart komputer** jika perlu

## 🔧 Advanced Troubleshooting

### **Debug Mode**
Tambahkan logging lebih detail:
```javascript
// Tambah di bot.js untuk debug
console.log('Debug: API Response:', response.data);
```

### **Network Issues**
```bash
# Test koneksi API
curl https://gagapi.onrender.com/gear

# Test Telegram API
curl https://api.telegram.org/bot<TOKEN>/getMe
```

### **Process Monitoring**
```bash
# Lihat semua Node.js processes
tasklist | find "node.exe"

# Monitor resource usage
wmic process where name="node.exe" get ProcessId,PageFileUsage,WorkingSetSize
```
