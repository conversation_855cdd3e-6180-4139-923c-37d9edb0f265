const TelegramBot = require('node-telegram-bot-api');
const axios = require('axios');
const crypto = require('crypto');

// Konfigurasi Bot
const TELEGRAM_TOKEN = process.env.TELEGRAM_TOKEN || '**********************************************';
const CHAT_ID = process.env.CHAT_ID || '-1002811795675';

// Inisialisasi Bot
const bot = new TelegramBot(TELEGRAM_TOKEN, { polling: true });

// Storage untuk tracking perubahan
let lastDataHash = {
  stock: '',
  weather: ''
};

let lastFullUpdate = null;
let updateCount = 0;

// Penanganan Error Bot
bot.on('error', (error) => {
  console.error('❌ Bot error:', error.message);
  if (error.message.includes('409 Conflict')) {
    console.log('⚠️ Bot instance conflict detected. Stopping...');
    process.exit(1);
  }
});

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error.message);
  if (error.message.includes('409 Conflict')) {
    console.log('⚠️ Multiple bot instances detected. Stopping this instance...');
    process.exit(1);
  }
});

// Fungsi untuk membuat hash dari data
function createHash(data) {
  return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
}

// Fungsi untuk memberikan tips berdasarkan cuaca
function getWeatherTip(weatherType) {
  const tips = {
    'rain': 'Waktu terbaik untuk menanam! Pertumbuhan lebih cepat dan buah bisa jadi basah (2x nilai)',
    'sunny': 'Cuaca cerah bagus untuk harvest. Pastikan tanaman tidak kekurangan air',
    'cloudy': 'Cuaca mendung, pertumbuhan normal. Waktu yang baik untuk maintenance',
    'storm': 'Badai bisa merusak tanaman! Lindungi dengan gear yang tepat',
    'snow': 'Cuaca dingin memperlambat pertumbuhan. Gunakan greenhouse jika ada'
  };

  return tips[weatherType] || 'Pantau terus perubahan cuaca untuk strategi terbaik!';
}



// Fungsi untuk mendeteksi jenis perubahan
function detectChanges(newData, oldHashes) {
  const changes = {
    stock: false,
    weather: false,
    hasAnyChange: false
  };

  const newHashes = {
    stock: createHash(newData.stock),
    weather: createHash(newData.weather)
  };

  // Cek setiap jenis data
  Object.keys(newHashes).forEach(key => {
    if (newHashes[key] !== oldHashes[key]) {
      changes[key] = true;
      changes.hasAnyChange = true;
    }
  });

  return { changes, newHashes };
}

// Fungsi untuk membuat pesan berdasarkan jenis perubahan
function createUpdateMessage(data, changes) {
  const { stock, weather } = data;
  
  // Format data
  const gearList = stock.gear && stock.gear.length > 0
    ? stock.gear.map(g => `• ${g.name} (${g.quantity}x)`).join('\n')
    : 'Tidak tersedia';

  const seedList = stock.seed && stock.seed.length > 0
    ? stock.seed.map(s => `• ${s.name} (${s.quantity}x)`).join('\n')
    : 'Tidak tersedia';

  const eggList = stock.egg && stock.egg.length > 0
    ? stock.egg.map(e => `• ${e.name} (${e.quantity}x)`).join('\n')
    : 'Tidak tersedia';

  const currentTime = new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' });

  // Emoji untuk perubahan
  const changeEmojis = {
    stock: changes.stock ? '🆕' : '',
    weather: changes.weather ? '🌟' : ''
  };

  // Header berdasarkan jenis perubahan
  let header = '📊 *Update Grow A Garden*\n';
  if (changes.stock && changes.weather) {
    header = '🔥 *PERUBAHAN BESAR! Stock & Cuaca Berubah*\n';
  } else if (changes.stock) {
    header = '🛒 *STOCK BERUBAH!*\n';
  } else if (changes.weather) {
    header = '🌦️ *CUACA BERUBAH!*\n';
  }

  // Format weather dengan penjelasan
  const weatherStatus = weather.active ? '✅ Aktif' : '❌ Tidak Aktif';
  const weatherType = weather.type || 'Tidak diketahui';
  const weatherEmoji = {
    'rain': '🌧️',
    'sunny': '☀️',
    'cloudy': '☁️',
    'storm': '⛈️',
    'snow': '❄️'
  };

  const weatherIcon = weatherEmoji[weatherType] || '🌦️';
  const effectsList = weather.effects && weather.effects.length > 0
    ? weather.effects.map(effect => `• ${effect}`).join('\n')
    : 'Tidak ada efek khusus';

  const message = `${header}
${weatherIcon} *Cuaca Sekarang:* ${weatherType.toUpperCase()} (${weatherStatus}) ${changeEmojis.weather}

📋 *Penjelasan Cuaca:*
${effectsList}

💡 *Tips:* ${getWeatherTip(weatherType)}

🧰 *Gear Shop (Tools & Equipment):* ${changeEmojis.stock}
${gearList}
${stock.gear && stock.gear.length > 0 ? `📊 Total: ${stock.gear.length} jenis gear tersedia` : ''}

🌱 *Seed Shop (Bibit Tanaman):* ${changeEmojis.stock}
${seedList}
${stock.seed && stock.seed.length > 0 ? `📊 Total: ${stock.seed.length} jenis seed tersedia` : ''}

🥚 *Egg Shop (Telur Hewan):* ${changeEmojis.stock}
${eggList}
${stock.egg && stock.egg.length > 0 ? `📊 Total: ${stock.egg.length} jenis egg tersedia` : ''}

📅 *Update:* ${currentTime}

_🤖 Smart Bot - Update #${++updateCount} | Hanya kirim saat ada perubahan_
`.trim();

  return message;
}

// Fungsi Utama - Intelligent Update
async function intelligentUpdate(forceUpdate = false) {
  try {
    console.log('🔍 Mengecek perubahan data...');
    
    // Fetch data dari semua endpoint
    const [gearRes, seedRes, eggRes, weatherRes] = await Promise.all([
      axios.get('https://gagapi.onrender.com/gear', { timeout: 10000 }),
      axios.get('https://gagapi.onrender.com/seeds', { timeout: 10000 }),
      axios.get('https://gagapi.onrender.com/eggs', { timeout: 10000 }),
      axios.get('https://gagapi.onrender.com/weather', { timeout: 10000 })
    ]);

    const newData = {
      stock: {
        gear: gearRes.data,
        seed: seedRes.data,
        egg: eggRes.data
      },
      weather: weatherRes.data,
      restock: { timeLeft: 'n/a', timeManila: new Date().toISOString() } // API tidak menyediakan restock time
    };

    // Deteksi perubahan
    const { changes, newHashes } = detectChanges(newData, lastDataHash);

    // Log perubahan yang terdeteksi
    if (changes.hasAnyChange) {
      const changedItems = Object.keys(changes)
        .filter(key => changes[key] === true)
        .join(', ');
      console.log(`📊 Perubahan terdeteksi: ${changedItems}`);
    } else {
      console.log('✅ Tidak ada perubahan data');
    }

    // Kirim update jika ada perubahan atau dipaksa
    if (changes.hasAnyChange || forceUpdate) {
      const message = createUpdateMessage(newData, changes);
      
      await bot.sendMessage(CHAT_ID, message, { 
        parse_mode: 'Markdown',
        disable_web_page_preview: true 
      });

      console.log(`✅ Update #${updateCount} berhasil dikirim`);
      lastFullUpdate = new Date();
      
      // Update hash untuk tracking selanjutnya
      lastDataHash = newHashes;
    }

    // Kirim update berkala meski tidak ada perubahan (setiap 30 menit)
    const now = new Date();
    if (lastFullUpdate && (now - lastFullUpdate) > 30 * 60 * 1000) {
      console.log('⏰ Update berkala (30 menit)');
      const periodicMessage = createUpdateMessage(newData, { stock: false, weather: false });

      await bot.sendMessage(CHAT_ID, periodicMessage.replace('Update #', 'Update Berkala #'), {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });

      lastFullUpdate = now;
      lastDataHash = newHashes;
    }

  } catch (err) {
    console.error('❌ Error dalam intelligent update:', err.message);
    
    // Kirim pesan error
    const errorMessage = `
⚠️ *Error Sistem Bot*

🔍 *Detail:* ${err.message}
🕒 *Waktu:* ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}

_Bot akan mencoba lagi dalam 2 menit..._
`;

    try {
      await bot.sendMessage(CHAT_ID, errorMessage, { parse_mode: 'Markdown' });
    } catch (telegramErr) {
      console.error('❌ Gagal kirim pesan error:', telegramErr.message);
    }
  }
}

// Fungsi untuk cek restock time dan kirim alert
async function checkRestockAlert() {
  try {
    // API tidak menyediakan restock time, jadi kita skip fungsi ini
    // atau bisa implementasi logika restock berdasarkan waktu tertentu
    console.log('⏰ Restock alert check - API tidak menyediakan data restock time');

  } catch (error) {
    console.error('❌ Error cek restock alert:', error.message);
  }
}

// Command Handlers
bot.onText(/\/cek/, async (msg) => {
  const chatId = msg.chat.id;
  
  try {
    await bot.sendMessage(chatId, '🔄 Mengambil data terbaru...');
    await intelligentUpdate(true); // Force update
  } catch (error) {
    await bot.sendMessage(chatId, '❌ Gagal mengambil data. Coba lagi nanti.');
  }
});

bot.onText(/\/start/, async (msg) => {
  const chatId = msg.chat.id;
  const welcomeMessage = `
🤖 *Selamat datang di Smart Grow A Garden Bot!*

🧠 *Fitur Cerdas:*
• Hanya kirim update saat ada perubahan
• Alert restock 5 menit sebelum
• Update berkala setiap 30 menit
• Deteksi perubahan real-time

📋 *Perintah:*
• /cek - Update manual
• /status - Status bot
• /stats - Statistik update
• /help - Bantuan

_Bot by ONAD - Smart Detection System_
`;

  await bot.sendMessage(chatId, welcomeMessage, { parse_mode: 'Markdown' });
});

bot.onText(/\/status/, async (msg) => {
  const chatId = msg.chat.id;
  
  try {
    await axios.get('https://gagapi.onrender.com/gear', { timeout: 5000 });
    const apiStatus = testRes.status === 200;
    const currentTime = new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' });
    
    const statusMessage = `
📊 *Status Smart Bot*

🤖 *Bot:* ✅ Aktif
🌐 *API:* ${apiStatus ? '✅ Terhubung' : '❌ Bermasalah'}
🧠 *Smart Detection:* ✅ Aktif
📈 *Total Updates:* ${updateCount}
🕒 *Last Check:* ${currentTime}
⏰ *Update Terakhir:* ${lastFullUpdate ? lastFullUpdate.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' }) : 'Belum ada'}

_Cek setiap 2 menit, kirim hanya saat berubah_
`;

    await bot.sendMessage(chatId, statusMessage, { parse_mode: 'Markdown' });
  } catch (error) {
    await bot.sendMessage(chatId, '❌ Error mengecek status');
  }
});

bot.onText(/\/stats/, async (msg) => {
  const chatId = msg.chat.id;
  const uptime = process.uptime();
  const hours = Math.floor(uptime / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  
  const statsMessage = `
📈 *Statistik Bot*

🔢 *Total Updates:* ${updateCount}
⏱️ *Uptime:* ${hours}h ${minutes}m
🧠 *Mode:* Smart Detection
⚡ *Efisiensi:* Hanya kirim saat berubah
📊 *Interval Cek:* 2 menit
🔄 *Update Berkala:* 30 menit

_Menghemat bandwidth & spam_
`;

  await bot.sendMessage(chatId, statsMessage, { parse_mode: 'Markdown' });
});

bot.onText(/\/help/, async (msg) => {
  const chatId = msg.chat.id;
  const helpMessage = `
📖 *Smart Bot Help*

🧠 *Cara Kerja:*
Bot mengecek data setiap 2 menit, tapi hanya mengirim notifikasi saat ada perubahan pada:
• Stock items (gear/seed/egg)
• Cuaca & mutasi
• Waktu restock

🚨 *Alert System:*
• Restock alert 5 menit sebelum
• Update berkala setiap 30 menit
• Error notification

🔧 *Commands:*
• /cek - Force update manual
• /status - Cek koneksi & status
• /stats - Lihat statistik bot
• /help - Bantuan ini

❓ *Support:* @ONAD
`;

  await bot.sendMessage(chatId, helpMessage, { parse_mode: 'Markdown' });
});

// Schedulers
// Cek perubahan setiap 2 menit
setInterval(() => {
  console.log('🔍 Scheduled check...');
  intelligentUpdate();
}, 2 * 60 * 1000);

// Cek restock alert setiap 1 menit
setInterval(() => {
  checkRestockAlert();
}, 60 * 1000);

// Graceful Shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Smart Bot sedang berhenti...');
  console.log(`📊 Total updates dikirim: ${updateCount}`);
  bot.stopPolling();
  console.log('✅ Bot berhasil dihentikan');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Smart Bot sedang berhenti...');
  console.log(`📊 Total updates dikirim: ${updateCount}`);
  bot.stopPolling();
  console.log('✅ Bot berhasil dihentikan');
  process.exit(0);
});

// Startup Bot
async function startSmartBot() {
  console.log('🚀 Memulai Smart Grow A Garden Bot...');
  console.log('🧠 Mode: Intelligent Detection System');
  
  try {
    // Test koneksi
    await axios.get('https://gagapi.onrender.com/gear', { timeout: 10000 });
    console.log('✅ API connection successful');
    
    // Test Telegram
    await bot.sendMessage(CHAT_ID, '🤖 *Smart Bot Started!*\n\n🧠 Intelligent detection system aktif\n⏰ Cek setiap 2 menit, kirim hanya saat berubah', { parse_mode: 'Markdown' });
    console.log('✅ Telegram connection successful');
    
    console.log('🤖 Smart Bot siap dan berjalan...');
    console.log('🔍 Cek perubahan setiap 2 menit');
    console.log('🚨 Restock alert setiap 1 menit');
    
    // Initial update setelah 5 detik
    setTimeout(() => {
      console.log('📊 Sending initial update...');
      intelligentUpdate(true);
    }, 5000);
    
  } catch (error) {
    console.error('❌ Startup error:', error.message);
    console.log('⚠️ Bot tetap berjalan, akan mencoba koneksi ulang...');
  }
}

// Jalankan Smart Bot
startSmartBot();