@echo off
echo 🔍 Checking Smart Grow A Garden Bot Status...
echo.

echo 📋 Node.js processes:
tasklist /fi "imagename eq node.exe" 2>nul | find "node.exe" >nul
if errorlevel 1 (
    echo ❌ No Node.js processes running
) else (
    tasklist /fi "imagename eq node.exe"
)

echo.
echo 🔒 Lock file status:
if exist bot.lock (
    echo ✅ Lock file exists
    set /p pid=<bot.lock
    echo 📍 PID in lock file: %pid%
    
    tasklist /fi "pid eq %pid%" 2>nul | find "%pid%" >nul
    if errorlevel 1 (
        echo ❌ Process with PID %pid% not found (stale lock file)
        echo 🗑️ Removing stale lock file...
        del bot.lock
    ) else (
        echo ✅ Bot process is running with PID %pid%
    )
) else (
    echo ❌ No lock file found
)

echo.
echo 💡 Commands:
echo   - restart_bot.bat : Restart bot safely
echo   - node bot.js     : Start bot manually
echo.

pause
