const TelegramBot = require('node-telegram-bot-api');
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Konfigurasi <PERSON>t
const TELEGRAM_TOKEN = process.env.TELEGRAM_TOKEN || '**********************************************';
const CHAT_ID = process.env.CHAT_ID || '-1002811795675';

// Lock file untuk mencegah multiple instance
const LOCK_FILE = path.join(__dirname, 'rarity-bot.lock');

// Inisialisasi Bot
const bot = new TelegramBot(TELEGRAM_TOKEN, { 
  polling: {
    interval: 1000,
    autoStart: true,
    params: { timeout: 10 }
  }
});

// RARITY MAPPING berdasarkan data yang dikumpulkan
const SEEDS_RARITY = {
  // TRANSCENDENT
  'Bone Blossom': 'TRANSCENDENT',
  
  // PRISMATIC  
  'Sugar Apple': 'PRISMATIC',
  'Burning Bud': 'PRISMATIC',
  'Giant Pinecone': 'PRISMATIC',
  'Elder Strawberry': 'PRISMATIC',
  'Tranquil Bloom': 'PRISMATIC',
  
  // DIVINE
  'Loquat': 'DIVINE',
  'Feijoa': 'DIVINE', 
  'Pitcher Plant': 'DIVINE',
  'Maple Apple': 'DIVINE',
  'Dragon Pepper': 'DIVINE',
  
  // MYTHICAL
  'Pineapple': 'MYTHICAL',
  'Kiwi': 'MYTHICAL',
  'Bell Pepper': 'MYTHICAL',
  'Prickly Pear': 'MYTHICAL',
  'Spiked Mango': 'MYTHICAL',
  'Coconut': 'MYTHICAL',
  'Mango': 'MYTHICAL',
  'Dragon Fruit': 'MYTHICAL',
  'Cactus': 'MYTHICAL',
  'Peach': 'MYTHICAL',
  'Rosy Delight': 'MYTHICAL',
  'Elephant Ears': 'MYTHICAL',
  
  // LEGENDARY
  'Watermelon': 'LEGENDARY',
  'Raffleisa': 'LEGENDARY',
  'Green Apple': 'LEGENDARY',
  'Avocado': 'LEGENDARY',
  'Banana': 'LEGENDARY',
  'Soft Sunshine': 'LEGENDARY',
  'Apple': 'LEGENDARY',
  'Bamboo': 'LEGENDARY',
  'Daffodil': 'LEGENDARY',
  'Pumpkin': 'LEGENDARY',
  'Cantaloupe': 'LEGENDARY',
  'Parasol Flower': 'LEGENDARY'
};

const EGGS_RARITY = {
  // PRISMATIC
  'Zen Egg': 'PRISMATIC',
  
  // DIVINE
  'Night Egg': 'DIVINE',
  'Oasis Egg': 'DIVINE', 
  'Dinosaur Egg': 'DIVINE',
  'Primal Egg': 'DIVINE',
  
  // SPECIAL/EVENT
  'Paradise Egg': 'SPECIAL',
  'Exotic Bug Egg': 'SPECIAL',
  
  // MYTHICAL
  'Mythical Egg': 'MYTHICAL',
  'Bug Egg': 'MYTHICAL',
  'Bee Egg': 'MYTHICAL',
  'Anti Bee Egg': 'MYTHICAL',
  
  // LEGENDARY
  'Legendary Egg': 'LEGENDARY'
};

const GEAR_RARITY = {
  // PRISMATIC
  'Levelup Lollipop': 'PRISMATIC',

  // SPECIAL/EVENT
  'Night Staff': 'SPECIAL',
  'Star Caller': 'SPECIAL',
  'Nectar Staff': 'SPECIAL',
  'Honey Sprinkler': 'SPECIAL',

  // DIVINE (Master Sprinkler)
  'Master Sprinkler': 'DIVINE',

  // MYTHICAL (Godly Sprinkler)
  'Godly Sprinkler': 'MYTHICAL',

  // LEGENDARY (Advanced Sprinkler)
  'Advanced Sprinkler': 'LEGENDARY',

  // RARE (Basic Sprinkler)
  'Basic Sprinkler': 'RARE'
};

// Priority levels yang kita monitor
const MONITORED_RARITIES = {
  seeds: ['MYTHICAL', 'DIVINE', 'PRISMATIC', 'TRANSCENDENT'], // LEGENDARY dihilangkan
  eggs: ['LEGENDARY', 'MYTHICAL', 'DIVINE', 'PRISMATIC', 'SPECIAL'],
  gear: ['RARE', 'LEGENDARY', 'MYTHICAL', 'DIVINE', 'SPECIAL', 'PRISMATIC'] // Hanya Basic/Advanced/Godly/Master Sprinkler + SPECIAL/PRISMATIC
};

// Emoji untuk setiap rarity
const RARITY_EMOJIS = {
  'TRANSCENDENT': '🌈',
  'PRISMATIC': '🌟',
  'DIVINE': '✨',
  'MYTHICAL': '🔥',
  'LEGENDARY': '⚡',
  'RARE': '💎',
  'SPECIAL': '🎉'
};

// Storage untuk tracking item yang sudah dikirim
let lastNotifiedItems = {
  seeds: new Set(),
  eggs: new Set(),
  gear: new Set()
};

let updateCount = 0;

// Lock file functions
function checkLockFile() {
  if (fs.existsSync(LOCK_FILE)) {
    try {
      const pid = fs.readFileSync(LOCK_FILE, 'utf8');
      try {
        process.kill(pid, 0);
        console.log('❌ Rarity Bot sudah berjalan dengan PID:', pid);
        process.exit(1);
      } catch (e) {
        fs.unlinkSync(LOCK_FILE);
      }
    } catch (e) {
      fs.unlinkSync(LOCK_FILE);
    }
  }
  fs.writeFileSync(LOCK_FILE, process.pid.toString());
  console.log('🔒 Rarity Bot lock file created with PID:', process.pid);
}

function removeLockFile() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      fs.unlinkSync(LOCK_FILE);
      console.log('🔓 Rarity Bot lock file removed');
    }
  } catch (e) {
    console.error('❌ Error removing lock file:', e.message);
  }
}

// Error handling
bot.on('error', (error) => {
  console.error('❌ Bot error:', error.message);
  if (error.message.includes('409 Conflict')) {
    console.log('⚠️ Bot instance conflict detected. Stopping...');
    removeLockFile();
    process.exit(1);
  }
});

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error.message);
  if (error.message.includes('409 Conflict')) {
    console.log('⚠️ Multiple bot instances detected. Stopping this instance...');
    removeLockFile();
    process.exit(1);
  }
});

// Fungsi untuk mendapatkan rarity item
function getItemRarity(itemName, category) {
  const rarityMap = {
    seeds: SEEDS_RARITY,
    eggs: EGGS_RARITY,
    gear: GEAR_RARITY
  };
  
  return rarityMap[category][itemName] || null;
}

// Fungsi untuk cek apakah item perlu dinotifikasi
function shouldNotify(itemName, category) {
  const rarity = getItemRarity(itemName, category);
  if (!rarity) return false;
  
  return MONITORED_RARITIES[category].includes(rarity);
}

// Fungsi untuk membuat pesan alert
function createRarityAlert(rareItems) {
  if (rareItems.length === 0) return null;

  const currentTime = new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' });

  // Tentukan tingkat alert berdasarkan rarity tertinggi
  const highestRarity = getHighestRarity(rareItems);
  const alertHeader = getAlertHeader(highestRarity);

  let message = `${alertHeader}\n\n`;

  // Group by category
  const categories = { seeds: [], eggs: [], gear: [] };

  rareItems.forEach(item => {
    categories[item.category].push(item);
  });

  // Seeds section
  if (categories.seeds.length > 0) {
    message += '🌱 *HIGH-TIER SEEDS:*\n';
    categories.seeds.forEach(item => {
      const emoji = RARITY_EMOJIS[item.rarity];
      message += `${emoji} *${item.rarity}* - ${item.name} (${item.quantity}x)\n`;
    });
    message += '\n';
  }

  // Eggs section
  if (categories.eggs.length > 0) {
    message += '🥚 *HIGH-TIER EGGS:*\n';
    categories.eggs.forEach(item => {
      const emoji = RARITY_EMOJIS[item.rarity];
      message += `${emoji} *${item.rarity}* - ${item.name} (${item.quantity}x)\n`;
    });
    message += '\n';
  }

  // Gear section
  if (categories.gear.length > 0) {
    message += '🧰 *HIGH-TIER GEAR:*\n';
    categories.gear.forEach(item => {
      const emoji = RARITY_EMOJIS[item.rarity];
      message += `${emoji} *${item.rarity}* - ${item.name} (${item.quantity}x)\n`;
    });
    message += '\n';
  }

  message += `⏰ *Alert Time:* ${currentTime}\n`;
  message += `🤖 *High-Tier Alert #${++updateCount}*`;

  return message;
}

// Fungsi untuk mendapatkan rarity tertinggi
function getHighestRarity(items) {
  const rarityOrder = ['RARE', 'LEGENDARY', 'MYTHICAL', 'DIVINE', 'PRISMATIC', 'TRANSCENDENT', 'SPECIAL'];
  let highest = 'RARE';

  items.forEach(item => {
    if (rarityOrder.indexOf(item.rarity) > rarityOrder.indexOf(highest)) {
      highest = item.rarity;
    }
  });

  return highest;
}

// Fungsi untuk mendapatkan header alert berdasarkan rarity
function getAlertHeader(rarity) {
  const headers = {
    'TRANSCENDENT': '🌈 *TRANSCENDENT ALERT!*',
    'PRISMATIC': '🌟 *PRISMATIC ALERT!*',
    'DIVINE': '✨ *DIVINE ALERT!*',
    'MYTHICAL': '🔥 *MYTHICAL ALERT!*',
    'LEGENDARY': '⚡ *LEGENDARY ALERT!*',
    'SPECIAL': '🎉 *SPECIAL EVENT ALERT!*',
    'RARE': '💎 *HIGH-TIER ALERT!*'
  };

  return headers[rarity] || '🚨 *HIGH-TIER ALERT!*';
}

// Fungsi untuk fetch dan cek rare items
async function checkRareItems() {
  try {
    console.log('🔍 Checking for rare items...');

    // Fetch data dari API
    const [gearRes, seedRes, eggRes] = await Promise.all([
      axios.get('https://gagapi.onrender.com/gear', { timeout: 15000 }),
      axios.get('https://gagapi.onrender.com/seeds', { timeout: 15000 }),
      axios.get('https://gagapi.onrender.com/eggs', { timeout: 15000 })
    ]);

    const rareItems = [];
    const currentItems = { seeds: new Set(), eggs: new Set(), gear: new Set() };

    // Check seeds
    seedRes.data.forEach(item => {
      currentItems.seeds.add(item.name);
      if (shouldNotify(item.name, 'seeds')) {
        const rarity = getItemRarity(item.name, 'seeds');
        if (!lastNotifiedItems.seeds.has(item.name)) {
          rareItems.push({
            name: item.name,
            quantity: item.quantity,
            rarity: rarity,
            category: 'seeds'
          });
          console.log(`🌱 RARE SEED FOUND: ${rarity} - ${item.name}`);
        }
      }
    });

    // Check eggs
    eggRes.data.forEach(item => {
      currentItems.eggs.add(item.name);
      if (shouldNotify(item.name, 'eggs')) {
        const rarity = getItemRarity(item.name, 'eggs');
        if (!lastNotifiedItems.eggs.has(item.name)) {
          rareItems.push({
            name: item.name,
            quantity: item.quantity,
            rarity: rarity,
            category: 'eggs'
          });
          console.log(`🥚 RARE EGG FOUND: ${rarity} - ${item.name}`);
        }
      }
    });

    // Check gear
    gearRes.data.forEach(item => {
      currentItems.gear.add(item.name);
      if (shouldNotify(item.name, 'gear')) {
        const rarity = getItemRarity(item.name, 'gear');
        if (!lastNotifiedItems.gear.has(item.name)) {
          rareItems.push({
            name: item.name,
            quantity: item.quantity,
            rarity: rarity,
            category: 'gear'
          });
          console.log(`🧰 RARE GEAR FOUND: ${rarity} - ${item.name}`);
        }
      }
    });

    // Update tracking sets
    lastNotifiedItems.seeds = currentItems.seeds;
    lastNotifiedItems.eggs = currentItems.eggs;
    lastNotifiedItems.gear = currentItems.gear;

    // Send alert if rare items found
    if (rareItems.length > 0) {
      const alertMessage = createRarityAlert(rareItems);
      if (alertMessage) {
        await bot.sendMessage(CHAT_ID, alertMessage, {
          parse_mode: 'Markdown',
          disable_web_page_preview: true
        });
        console.log(`🚨 RARE ITEM ALERT SENT! Found ${rareItems.length} rare items`);
      }
    } else {
      console.log('✅ No new rare items found');
    }

  } catch (error) {
    console.error('❌ Error checking rare items:', error.message);
  }
}

// Bot commands
bot.onText(/\/start/, (msg) => {
  const welcomeMessage = `
🤖 *Grow A Garden Rarity Bot*

🎯 *Fungsi:* Monitor item langka di Grow A Garden
📊 *Yang Dimonitor:*

🌱 *Seeds:* MYTHICAL, DIVINE, PRISMATIC, TRANSCENDENT (LEGENDARY dihilangkan)
🥚 *Eggs:* LEGENDARY, MYTHICAL, DIVINE, PRISMATIC, SPECIAL
🧰 *Gear:* Basic Sprinkler, Advanced Sprinkler, Godly Sprinkler, Master Sprinkler, SPECIAL/EVENT, PRISMATIC

⚡ *Hanya kirim alert jika ada item langka!*

📋 *Commands:*
/start - Info bot
/check - Cek manual rare items
/status - Status bot dan API
/stats - Statistik alert

🔥 *Bot siap memantau item langka!*
`;

  bot.sendMessage(msg.chat.id, welcomeMessage, { parse_mode: 'Markdown' });
});

bot.onText(/\/check/, async (msg) => {
  await bot.sendMessage(msg.chat.id, '🔍 Checking for rare items manually...');
  await checkRareItems();
});

bot.onText(/\/status/, async (msg) => {
  try {
    await axios.get('https://gagapi.onrender.com/gear', { timeout: 5000 });
    const statusMessage = `
✅ *Rarity Bot Status*

🌐 *API:* Connected
🤖 *Bot:* Running
📊 *Total Alerts:* ${updateCount}
⏰ *Last Check:* ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}

🎯 *Monitoring:* Rare items only
`;
    await bot.sendMessage(msg.chat.id, statusMessage, { parse_mode: 'Markdown' });
  } catch (error) {
    await bot.sendMessage(msg.chat.id, '❌ API connection failed', { parse_mode: 'Markdown' });
  }
});

bot.onText(/\/stats/, (msg) => {
  const statsMessage = `
📊 *Rarity Bot Statistics*

🚨 *Total Alerts Sent:* ${updateCount}
⏰ *Bot Uptime:* ${process.uptime().toFixed(0)} seconds
🎯 *Mode:* Rare Items Only

🔍 *Currently Tracking:*
🌱 Seeds: ${Object.keys(SEEDS_RARITY).length} rare items
🥚 Eggs: ${Object.keys(EGGS_RARITY).length} rare items
🧰 Gear: ${Object.keys(GEAR_RARITY).length} rare items
`;

  bot.sendMessage(msg.chat.id, statsMessage, { parse_mode: 'Markdown' });
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Rarity Bot stopping...');
  console.log(`📊 Total alerts sent: ${updateCount}`);
  bot.stopPolling();
  removeLockFile();
  console.log('✅ Rarity Bot stopped');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Rarity Bot stopping...');
  bot.stopPolling();
  removeLockFile();
  process.exit(0);
});

process.on('exit', () => {
  removeLockFile();
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  removeLockFile();
  process.exit(1);
});

// Startup function
async function startRarityBot() {
  checkLockFile();

  console.log('🚨 Starting Grow A Garden Rarity Bot...');
  console.log('🎯 Mode: Rare Items Only Alert System');

  try {
    // Test API connection
    await axios.get('https://gagapi.onrender.com/gear', { timeout: 15000 });
    console.log('✅ API connection successful');

    // Test Telegram connection
    await bot.getMe();
    console.log('✅ Telegram connection successful');

    console.log('🤖 Rarity Bot ready and running...');
    console.log('🔍 Checking for rare items every 2 minutes');
    console.log(`📊 Monitoring ${Object.keys(SEEDS_RARITY).length} seeds, ${Object.keys(EGGS_RARITY).length} eggs, ${Object.keys(GEAR_RARITY).length} gear`);

    // Initial check
    console.log('🚨 Performing initial rare items check...');
    await checkRareItems();

    // Set interval untuk cek rare items setiap 2 menit
    setInterval(checkRareItems, 2 * 60 * 1000);

  } catch (error) {
    console.error('❌ Startup error:', error.message);
    console.log('⚠️ Bot will keep trying to reconnect...');

    // Retry connection setiap 30 detik
    setTimeout(startRarityBot, 30000);
  }
}

// Start the bot
startRarityBot();
