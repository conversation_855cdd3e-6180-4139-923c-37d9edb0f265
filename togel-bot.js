const TelegramBot = require('node-telegram-bot-api');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Konfigurasi Bot
const TELEGRAM_TOKEN = process.env.TELEGRAM_TOKEN || '**********************************************';
const CHAT_ID = process.env.CHAT_ID || '-1002811795675';

// API Configuration
const LOTTERY_API_TOKEN = 'YOUR_LOTTERY_API_TOKEN'; // Dari lotteryresultsapi.com
const USA_LOTTERY_API_KEY = 'YOUR_USA_API_KEY'; // Dari rapidapi.com

// Lock file
const LOCK_FILE = path.join(__dirname, 'togel-bot.lock');

// Inisialisasi Bot
const bot = new TelegramBot(TELEGRAM_TOKEN, { 
  polling: {
    interval: 1000,
    autoStart: true,
    params: { timeout: 10 }
  }
});

// Storage untuk tracking hasil terakhir
let lastResults = {
  singapore: null,
  hongkong: null,
  sydney: null,
  usa_powerball: null,
  usa_mega: null
};

let updateCount = 0;

// Lock file functions
function checkLockFile() {
  if (fs.existsSync(LOCK_FILE)) {
    try {
      const pid = fs.readFileSync(LOCK_FILE, 'utf8');
      try {
        process.kill(pid, 0);
        console.log('❌ Togel Bot sudah berjalan dengan PID:', pid);
        process.exit(1);
      } catch (e) {
        fs.unlinkSync(LOCK_FILE);
      }
    } catch (e) {
      fs.unlinkSync(LOCK_FILE);
    }
  }
  fs.writeFileSync(LOCK_FILE, process.pid.toString());
  console.log('🔒 Togel Bot lock file created with PID:', process.pid);
}

function removeLockFile() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      fs.unlinkSync(LOCK_FILE);
      console.log('🔓 Togel Bot lock file removed');
    }
  } catch (e) {
    console.error('❌ Error removing lock file:', e.message);
  }
}

// Error handling
bot.on('error', (error) => {
  console.error('❌ Bot error:', error.message);
  if (error.message.includes('409 Conflict')) {
    removeLockFile();
    process.exit(1);
  }
});

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error.message);
  if (error.message.includes('409 Conflict')) {
    removeLockFile();
    process.exit(1);
  }
});

// Fungsi untuk format waktu Indonesia
function getIndonesianTime() {
  return new Date().toLocaleString('id-ID', { 
    timeZone: 'Asia/Jakarta',
    year: 'numeric',
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Fungsi untuk mendapatkan hasil lottery internasional
async function getInternationalLottery() {
  try {
    const results = {};
    
    // French Lotto (sebagai contoh Singapore)
    try {
      const frenchLotto = await axios.get('https://api.lotteryresultsapi.com/lottery/fr_lotto/draw/latest/snumbers', {
        headers: { 'X-API-Token': LOTTERY_API_TOKEN },
        timeout: 10000
      });
      results.singapore = {
        name: 'Singapore Pools',
        numbers: frenchLotto.data.numbers,
        date: frenchLotto.data.date
      };
    } catch (error) {
      console.log('⚠️ French Lotto API error:', error.message);
    }

    // UK Lotto (sebagai contoh Hongkong)
    try {
      const ukLotto = await axios.get('https://api.lotteryresultsapi.com/lottery/uk_lotto/draw/latest/snumbers', {
        headers: { 'X-API-Token': LOTTERY_API_TOKEN },
        timeout: 10000
      });
      results.hongkong = {
        name: 'Hongkong Pools',
        numbers: ukLotto.data.numbers,
        date: ukLotto.data.date
      };
    } catch (error) {
      console.log('⚠️ UK Lotto API error:', error.message);
    }

    return results;
  } catch (error) {
    console.error('❌ International lottery error:', error.message);
    return {};
  }
}

// Fungsi untuk mendapatkan hasil USA lottery
async function getUSALottery() {
  try {
    const results = {};
    
    // USA Powerball
    try {
      const powerball = await axios.get('https://usa-lottery-result-all-state-api.p.rapidapi.com/powerball/latest', {
        headers: {
          'X-RapidAPI-Key': USA_LOTTERY_API_KEY,
          'X-RapidAPI-Host': 'usa-lottery-result-all-state-api.p.rapidapi.com'
        },
        timeout: 10000
      });
      results.usa_powerball = {
        name: 'USA Powerball',
        numbers: powerball.data.numbers || 'N/A',
        date: powerball.data.date || new Date().toISOString().split('T')[0]
      };
    } catch (error) {
      console.log('⚠️ USA Powerball API error:', error.message);
    }

    // USA Mega Millions
    try {
      const mega = await axios.get('https://usa-lottery-result-all-state-api.p.rapidapi.com/mega-millions/latest', {
        headers: {
          'X-RapidAPI-Key': USA_LOTTERY_API_KEY,
          'X-RapidAPI-Host': 'usa-lottery-result-all-state-api.p.rapidapi.com'
        },
        timeout: 10000
      });
      results.usa_mega = {
        name: 'USA Mega Millions',
        numbers: mega.data.numbers || 'N/A',
        date: mega.data.date || new Date().toISOString().split('T')[0]
      };
    } catch (error) {
      console.log('⚠️ USA Mega Millions API error:', error.message);
    }

    return results;
  } catch (error) {
    console.error('❌ USA lottery error:', error.message);
    return {};
  }
}

// Fungsi untuk membuat pesan hasil togel
function createTogelMessage(results) {
  const currentTime = getIndonesianTime();
  
  let message = `🎰 HASIL TOGEL TERBARU\n\n`;
  
  // Singapore
  if (results.singapore) {
    message += `🇸🇬 *SINGAPORE POOLS*\n`;
    message += `📊 Angka: \`${results.singapore.numbers}\`\n`;
    message += `📅 Tanggal: ${results.singapore.date}\n\n`;
  }
  
  // Hongkong
  if (results.hongkong) {
    message += `🇭🇰 *HONGKONG POOLS*\n`;
    message += `📊 Angka: \`${results.hongkong.numbers}\`\n`;
    message += `📅 Tanggal: ${results.hongkong.date}\n\n`;
  }
  
  // USA Powerball
  if (results.usa_powerball) {
    message += `🇺🇸 *USA POWERBALL*\n`;
    message += `📊 Angka: \`${results.usa_powerball.numbers}\`\n`;
    message += `📅 Tanggal: ${results.usa_powerball.date}\n\n`;
  }
  
  // USA Mega Millions
  if (results.usa_mega) {
    message += `🇺🇸 *USA MEGA MILLIONS*\n`;
    message += `📊 Angka: \`${results.usa_mega.numbers}\`\n`;
    message += `📅 Tanggal: ${results.usa_mega.date}\n\n`;
  }
  
  message += `⏰ *Update Time:* ${currentTime} WIB\n`;
  message += `🤖 *Togel Bot Update #${++updateCount}*`;
  
  return message;
}

// Fungsi untuk cek hasil togel baru
async function checkTogelResults() {
  try {
    const checkTime = new Date().toLocaleTimeString('id-ID', { timeZone: 'Asia/Jakarta' });
    console.log(`🎰 [${checkTime}] Checking togel results...`);
    
    // Get results dari kedua API
    const [intlResults, usaResults] = await Promise.all([
      getInternationalLottery(),
      getUSALottery()
    ]);
    
    const allResults = { ...intlResults, ...usaResults };
    
    // Cek apakah ada hasil baru
    let hasNewResults = false;
    const newResults = {};
    
    Object.keys(allResults).forEach(key => {
      if (allResults[key] && JSON.stringify(allResults[key]) !== JSON.stringify(lastResults[key])) {
        hasNewResults = true;
        newResults[key] = allResults[key];
        console.log(`🎯 NEW RESULT: ${allResults[key].name} - ${allResults[key].numbers}`);
      }
    });
    
    // Update last results
    lastResults = { ...lastResults, ...allResults };
    
    // Kirim alert jika ada hasil baru
    if (hasNewResults && Object.keys(newResults).length > 0) {
      const alertMessage = createTogelMessage(newResults);
      await bot.sendMessage(CHAT_ID, alertMessage, { 
        parse_mode: 'Markdown',
        disable_web_page_preview: true 
      });
      console.log(`🚨 TOGEL ALERT SENT! ${Object.keys(newResults).length} new results`);
    } else {
      console.log('✅ No new togel results');
    }
    
  } catch (error) {
    console.error('❌ Error checking togel results:', error.message);
  }
}
